Config	=	{}

Config.CheckOwnership = true -- If true, Only owner of vehicle can store items in trunk.
Config.AllowPolice = true -- If true, police will be able to search players' trunks.

-- إعدادات الحماية من القلتشات - محسنة للحماية القوية
Config.AutoCloseDistance = 2.5 -- المسافة بالمتر التي يتم إغلاق الحقيبة عندها تلقائياً (مقللة للحماية الأقوى)
Config.MenuCloseDistance = 3.0 -- المسافة لإغلاق القائمة أثناء الاستخدام (مقللة للحماية الأقوى)
Config.AntiGlitchEnabled = true -- تفعيل الحماية من القلتشات

Config.Locale   = 'en'

 -- Limit, unit can be whatever you want. Originally grams (as average people can hold 25kg)
Config.Limit = 150

-- Default weight for an item:
	-- weight == 0 : The item do not affect character inventory weight
	-- weight > 0 : The item cost place on inventory
	-- weight < 0 : The item add place on inventory. Smart people will love it.
Config.DefaultWeight = 100

Config.localWeight = {

	bread = 125,
    water = 336,		
    diamond = 500,	
	v_tool = 2000, -- عدة خضروات
	--المعادن
	m_tool = 5700, -- عدة المعادن --limit : 7
	minerToolbox = 39900, -- 7
	stones = 5700,
    washedstones = 5700,
	gold = 336,	
	iron = 400,
    copper = 336,
    aluminum = 166.6666,
	--النفط
	f_tool = 1000, -- نفط وغاز --limit : 48
	--fueler_kit_box 	= 32000, --48  
	petrol 			= 1000,
	petrol_raffin 	= 2000,
    essence 		= 1000,
	--الغاز
	gas 			= 1000,
	gas_raffin 		= 2000,
    gazbottle 		= 1000,
	--الدواجن
	s_tool     = 2000, --limit : 20
	slaughtererToolbox = 40000, --20
	alive_chicken 		= 2000,
    slaughtered_chicken = 2000,
    packaged_chicken 	= 400,
	--الاخشاب
	l_tool		= 2000, --limit : 20
	lumberjackToolbox 	= 40000, --20
	wood 				= 2000,
    cutted_wood 		= 2000,
	packaged_plank 		= 400,
	--اقمشة
	t_tool		= 1000, --limit : 40
	tailorToolbox 	= 40000, --40
	wool 			= 1000,
	fabric 			= 500,
    clothe 			= 500,
	--سمك
	fishingrod 		= 400,	--limit : 100
	fishbaitbox 	= 40000, --400
	fish 			= 1000,
	shark 			= 10000,
	turtle 			= 4000,
	
	fishingrod 		= 500,
	fishingrod_box 	= 2000,
	
	turtlebait 		= 250,
	turtlebaitbox 	= 2500,
	
	fishbait 		= 250,
	fishbaitbox 	= 7500,
	--المخدرات
	weed 		= 200,
    weed_pooch 	= 1000,	
    coke 		= 200,
	coke_pooch 	= 1000,
	meth 		= 200,
	meth_pooch 	= 1000,
	opium 		= 200,
	opium_pooch = 1000,
	--أشياء إضافية لم يتم التأكد من جميعها
	black_chip 	= 50,	
    flashlight 	= 500,
	grip 		= 250,
	lowrider 	= 250,
	yusuf 		= 250,
	silent 		= 500,
	scope 		= 250,
	advanced_scope = 250,
	extended_magazine = 500,	 
    very_extended_magazine = 1000,
	
	clip 		= 2000, --limit : 2
	clip_box 	= 16000, --8 
	
	bulletproof = 1000,	--limit : 1
	bulletproof_box = 4000, --4
	
	oxygen_mask = 2000, --limit : 2
	oxygen_mask_box = 4000, --8
	
	drill 		= 10000, --limit : 1
	drill_box 	= 40000, --4  
	
	mask 		= 500, --limit : 1
	mask_box 	= 2000, --4
	
	headbag 	= 1000,  --limit : 1
	headbag_box = 4000,  --4
	
	jumelles = 3000, --limit : 1
	jumelles_box = 12000, --4
	
	speedcamera_detector = 2000, --limit : 1
	speedcamera_detector_box = 8000, --4 
	
	blowpipe 	= 1000,
	carokit 	= 1000,
    carotool 	= 1000,
	fixtool 	= 1000, 
	fixkit 		= 1000, --غدة تصليح
	
    raisin 		= 400, --grape عنب
	jus_raisin 	= 800, --grape juice عصير عنب
	vine 		= 800, --wine زجاجة خمر
	grand_cru 	= 800, --vintage wine زجاجة خمر فاخر
	
	stock_org 	= 20000, --بضاعة منظمة
	
	radio = 2000,
	radio_box = 10000,
	
	bag = 1000,
	bag_box = 4000,
	--
	
	v_ready = 1000, -- سلة خضروات جاهزة للبيع	
    weedpros = 1000,
    weedplant = 1000,	
    weed = 1000,
    weedbox = 4000,
    coke_pooch = 1000,	
    cokeplant = 1000,
    coke = 1000,
    cokebox = 4000,	
    demigrape = 2000,
    grape = 1000,
    beer = 1000,
    winebox = 4000,
    beer = 1000,
    acetone = 500,
    lithium = 500,
    meth = 1000,
    methbox = 4000,
    lowplat = 1000,
    highplat = 2000,
    lowmetal = 5000,
    highmetal = 10000,
    weapondesign = 3000,
	weaponcrafting = 20000,
    weaponos = 1000,
    weaponzi = 1000,
    low_wood = 1000,
    low_iron = 1000,
    jerry = 1000,
    oil = 5700,	
    still = 4444,
    washedstill = 4444,	
    meltedstill = 4444,
    stillmetal = 4444,		
	fixtool = 10000,
	waterbox = 2000,
	water = 200,
	phone = 500,
	breadbox = 2000,
	bread = 200,
	cocacolabox = 2000,	
	chocolatebox = 2000,
	chocolate = 200,
	cupcakebox = 2000,
	cupcake = 200,
	croquettesbox = 2000,
	croquettes = 200,
	phonebox = 2000,
	cigarettbox = 2000,	
	cigarett = 200,
	lighterbox = 2000,	
    --Guns	
    WEAPON_KNIFE = 5000,
    WEAPON_NIGHTSTICK = 5000,
	WEAPON_HAMMER = 5000,
	WEAPON_BAT = 5000,
    WEAPON_GOLFCLUB = 5000,
	WEAPON_CROWBAR = 5000,
	WEAPON_PISTOL = 20000,
	WEAPON_COMBATPISTOL = 20000,
	WEAPON_APPISTOL = 100000,
	WEAPON_PISTOL50 = 20000,
	WEAPON_MICROSMG = 20000,
	WEAPON_SMG = 100000,
	WEAPON_ASSAULTSMG = 100000,
	WEAPON_ASSAULTRIFLE = 100000,
	WEAPON_CARBINERIFLE = 20000,
	WEAPON_ADVANCEDRIFLE = 20000,
	WEAPON_MG = 100000,
	WEAPON_COMBATMG = 20000,
	WEAPON_PUMPSHOTGUN = 20000,
	WEAPON_SAWNOFFSHOTGUN = 100000,
	WEAPON_ASSAULTSHOTGUN = 100000,
	WEAPON_BULLPUPSHOTGUN = 100000,
	WEAPON_STUNGUN = 10000,
	WEAPON_SNIPERRIFLE = 35000,
	WEAPON_HEAVYSNIPER = 40000,
	WEAPON_REMOTESNIPER = 100000,
	WEAPON_GRENADELAUNCHER = 100000,
	WEAPON_RPG = 100000,
	WEAPON_STINGER = 100000,
	WEAPON_MINIGUN = 100000,
	WEAPON_GRENADE = 100000,
	WEAPON_STICKYBOMB = 100000,
	WEAPON_SMOKEGRENADE = 35000,
	WEAPON_BZGAS = 10000,
	WEAPON_MOLOTOV = 10000,
	WEAPON_FIREEXTINGUISHER = 5000,
	WEAPON_PETROLCAN = 15000,
	WEAPON_DIGISCANNER = 100000,
	WEAPON_BALL = 1000,
	WEAPON_SNSPISTOL = 20000,
	WEAPON_BOTTLE = 1000,
	WEAPON_GUSENBERG = 100000,
	WEAPON_SPECIALCARBINE = 20000,
	WEAPON_HEAVYPISTOL = 20000,
	WEAPON_BULLPUPRIFLE = 100000,
	WEAPON_DAGGER = 100000,
	WEAPON_VINTAGEPISTOL = 20000,
	WEAPON_FIREWORK = 35000,
	WEAPON_MUSKET = 100000,
	WEAPON_HEAVYSHOTGUN = 100000,
	WEAPON_MARKSMANRIFLE = 100000,
	WEAPON_HOMINGLAUNCHER = 100000,
	WEAPON_PROXMINE = 100000,
	WEAPON_SNOWBALL = 1000,
	WEAPON_FLAREGUN = 3000,
	WEAPON_GARBAGEBAG = 100000,
	WEAPON_HANDCUFFS = 100000,
	WEAPON_COMBATPDW = 100000,
	WEAPON_MARKSMANPISTOL = 3000,
	WEAPON_KNUCKLE = 3000,
	WEAPON_HATCHET = 3000,
	WEAPON_RAILGUN = 100000,
	WEAPON_MACHETE = 3000,
	WEAPON_MACHINEPISTOL = 100000,
	WEAPON_SWITCHBLADE = 3000,
	WEAPON_REVOLVER = 20000,
	WEAPON_DBSHOTGUN = 100000,
	WEAPON_COMPACTRIFLE = 20000,
	WEAPON_AUTOSHOTGUN = 100000,
	WEAPON_BATTLEAXE = 3000,
	WEAPON_COMPACTLAUNCHER = 100000,
	WEAPON_MINISMG = 20000,
	WEAPON_PIPEBOMB = 100000,
	WEAPON_POOLCUE = 5000,
	WEAPON_WRENCH = 5000,
	WEAPON_FLASHLIGHT = 5000,
	GADGET_NIGHTVISION = 5000,
	GADGET_PARACHUTE = 20000,
	WEAPON_FLARE = 3000,
	WEAPON_SNSPISTOL_MK2 = 100000,
	WEAPON_REVOLVER_MK2 = 100000,
	WEAPON_DOUBLEACTION = 20000,
	WEAPON_SPECIALCARBINE_MK2 = 100000,
	WEAPON_BULLPUPRIFLE_MK2 = 100000,
	WEAPON_PUMPSHOTGUN_MK2 = 100000,
	WEAPON_MARKSMANRIFLE_MK2 = 100000,
	WEAPON_ASSAULTRIFLE_MK2 = 100000,
	WEAPON_CARBINERIFLE_MK2 = 100000,
	WEAPON_COMBATMG_MK2 = 100000,
	WEAPON_HEAVYSNIPER_MK2 = 100000,
	WEAPON_PISTOL_MK2 = 100000,
	WEAPON_SMG_MK2 = 100000,
}

Config.VehicleLimit = {
    [0] = 15000, --Compact
    [1] = 40000, --Sedan
    [2] = 40000, --SUV
    [3] = 20000, --Coupes
    [4] = 25000, --Muscle
    [5] = 10000, --Sports Classics
    [6] = 15000, --Sports
    [7] = 15000, --Super
    [8] = 5000, --Motorcycles
    [9] = 40000, --Off-road
    [10] = 350000, --Industrial
    [11] = 40000, --Utility
    [12] = 50000, --Vans -- 40
    [13] = 0, --Cycles
    [14] = 5000, --Boats
    [15] = 20000, --Helicopters
    [16] = 450000, --Planes
    [17] = 40000, --Service
    [18] = 40000, --Emergency
    [19] = 450000, --Military
    [20] = 250000, --Commercial
    [21] = 50000 --Trains

}

Config.VehicleModel = {

	-- ========================================
	-- السيارات السيدان (28 سيارة) - حمولة 35000
	-- ========================================
	asea = 35000,
	asea2 = 35000,
	asterope = 35000,
	emperor = 35000,
	emperor2 = 35000,
	emperor3 = 35000,
	fugitive = 35000,
	glendale = 35000,
	ingot = 35000,
	intruder = 35000,
	premier = 35000,
	primo = 35000,
	primo2 = 35000,
	regina = 35000,
	schafter2 = 35000,
	stanier = 35000,
	stratum = 35000,
	stretch = 35000,
	surge = 35000,
	tailgater = 35000,
	warrener = 35000,
	washington = 35000,
	gcmpassat12 = 35000,
	lex350 = 35000,
	lex500 = 35000,
	optima = 35000,
	towncar91 = 35000,
	altima = 35000,
	cpr16 = 35000,

	-- السيارات الجديدة المضافة - فئة سيدان
	['79mudrunner'] = 35000,
	drehellcatdurango = 40000,  -- دوج درانجو
	lc300gr = 35000,
	bodhi2 = 35000,
	relaxgmc = 60000,  -- جمس سيرا 2017
	sierra2020 = 60000,  -- جمس سيرا 2020
	rebel2 = 40000,  -- هايلكس 1998
	gle22 = 35000,
	oracle2 = 35000,
	max99s = 35000,
	taurus23 = 35000,
	cm22 = 35000,
	premier = 35000,
	caprice17 = 35000,
	oracle = 35000,
	benzs600 = 35000,
	Camry11 = 35000,
	vic11 = 35000,
	gm2005 = 35000,

	-- ========================================
	-- السيارات الكبيرة SUV (17 سيارة) - حمولة 45000
	-- ========================================
	baller = 45000,
	baller2 = 45000,
	baller3 = 45000,
	baller4 = 45000,
	baller5 = 45000,
	baller6 = 45000,
	cavalcade = 45000,
	cavalcade2 = 45000,
	dubsta = 45000,
	dubsta2 = 45000,
	fq2 = 45000,
	granger = 45000,
	gresley = 45000,
	habanero = 45000,
	huntley = 45000,
	landstalker = 45000,
	gx21 = 45000,

	-- السيارات الجديدة المضافة - فئة SUV
	lc200 = 45000,
	patrol2020 = 45000,
	tahoe22 = 45000,
	yukon21 = 45000,
	expedition20 = 45000,
	sequoia19 = 45000,
	armada18 = 45000,
	qx8020 = 45000,
	gls21 = 45000,
	x720 = 45000,
	q719 = 45000,
	cayenne20 = 45000,
	rangerover21 = 45000,
	discovery20 = 45000,
	grandcherokee19 = 45000,

	-- ========================================
	-- السيارات الرياضية (90 سيارة) - حمولة 25000
	-- ========================================
	alpha = 25000,
	banshee = 25000,
	bestiaGTS = 25000,
	blista2 = 25000,
	blista3 = 25000,
	buffalo = 25000,
	buffalo2 = 25000,
	buffalo3 = 25000,
	carbonizzare = 25000,
	comet2 = 25000,
	comet3 = 25000,
	coquette = 25000,
	elegy = 25000,
	elegy2 = 25000,
	feltzer2 = 25000,
	feltzer3 = 25000,
	fusilade = 25000,
	futo = 25000,
	jester = 25000,
	jester2 = 25000,
	khamelion = 25000,
	kuruma = 25000,
	kuruma2 = 25000,
	lynx = 25000,
	massacro = 25000,
	massacro2 = 25000,
	ninef = 25000,
	ninef2 = 25000,
	omnis = 25000,
	penumbra = 25000,
	rapidgt = 25000,
	rapidgt2 = 25000,
	schafter3 = 25000,
	schafter4 = 25000,
	schafter5 = 25000,
	schafter6 = 25000,
	schwarzer = 25000,
	sentinel = 25000,
	sentinel2 = 25000,
	seven70 = 25000,
	specter = 25000,
	specter2 = 25000,
	sultan = 25000,
	surano = 25000,
	tampa = 25000,
	tropos = 25000,
	verlierer2 = 25000,
	-- Fast & Furious Collection
	['66fastback'] = 25000,
	['93mustang'] = 25000,
	stiwrc = 25000,
	['2f2fgts'] = 25000,
	fnflan = 25000,
	['2f2fmk4'] = 25000,
	ff4wrx = 25000,
	fnfmk4 = 25000,
	['2f2fmle7'] = 25000,
	fnf4r34 = 25000,
	['2f2fgtr34'] = 25000,
	fnfrx7 = 25000,
	fnfmits = 25000,
	['2f2frx7'] = 25000,
	['2f2fs2000'] = 25000,
	fnfjetta = 25000,
	fnfrx7dom = 25000,
	['350zdk'] = 25000,
	['350zm'] = 25000,
	hcej1 = 25000,
	silvias15 = 25000,
	brabus850 = 25000,
	shelbygt500 = 25000,
	skylineken = 25000,
	vega = 25000,
	fe86 = 25000,
	['968'] = 25000,
	ipl = 25000,
	pgt3 = 25000,
	game718 = 25000,
	gamea45 = 25000,
	gamef430s = 25000,
	firebird = 25000,
	gtr = 25000,
	bmwm8 = 25000,
	gtr2 = 25000,
	inf = 25000,
	['911rwb'] = 25000,
	mxpan = 25000,
	brz13 = 25000,
	terminator = 25000,
	foxshelby = 25000,
	foxsupra = 25000,
	p993t = 25000,
	['95zr1'] = 25000,
	rt10 = 25000,
	exige12 = 25000,
	z48 = 25000,
	czr1 = 25000,
	agta = 25000,
	alfa65 = 25000,
	al1 = 25000,
	amcj = 25000,
	['442'] = 25000,
	['718gt4'] = 25000,
	outlaw = 25000,
	trans69 = 25000,
	rmode63s = 25000,

	-- السيارات الجديدة المضافة - فئة سبورت
	huracan = 25000,
	f488 = 25000,
	mclaren720s = 25000,
	porsche911turbo = 25000,
	astondb11 = 25000,
	bentleycgt = 25000,
	jaguarftype = 25000,
	shelbygt500 = 25000,
	camarozl1 = 25000,
	dodgeviper = 25000,
	corvettec8 = 25000,
	gtrnismo = 25000,
	supramk5 = 25000,
	bmwm8comp = 25000,
	amggt = 25000,

	-- ========================================
	-- السيارات السوبر (106 سيارة) - حمولة 20000
	-- ========================================
	adder = 20000,
	banshee2 = 20000,
	bullet = 20000,
	cheetah = 20000,
	entityxf = 20000,
	fmj = 20000,
	gp1 = 20000,
	infernus = 20000,
	italigtb = 20000,
	italigtb2 = 20000,
	le7b = 20000,
	lynx = 20000,
	nero = 20000,
	nero2 = 20000,
	osiris = 20000,
	penetrator = 20000,
	pfister811 = 20000,
	prototipo = 20000,
	reaper = 20000,
	sc1 = 20000,
	sultanrs = 20000,
	t20 = 20000,
	tempesta = 20000,
	turismor = 20000,
	tyrus = 20000,
	vacca = 20000,
	voltic = 20000,
	voltic2 = 20000,
	xa21 = 20000,
	zentorno = 20000,
	ztype = 20000,
	-- R-Mod Collection
	rmodmustang = 20000,
	rmodgtr = 20000,
	c7r = 20000,
	zl12017 = 20000,
	s63w222 = 20000,
	slsamg = 20000,
	m3e46 = 20000,
	m3e92 = 20000,
	m2 = 20000,
	rmodfordgt = 20000,
	rmodskyline = 20000,
	benzsl63 = 20000,
	rm3e36 = 20000,
	ast = 20000,
	c6z06 = 20000,
	c7z06 = 20000,
	fk8 = 20000,
	exigev6 = 20000,
	p7 = 20000,
	['911r'] = 20000,
	m3gtr = 20000,
	z419 = 20000,
	focusrs = 20000,
	c8 = 20000,
	c8c = 20000,
	['2020ss'] = 20000,
	supraa90 = 20000,
	m82020 = 20000,
	dvgtsr = 20000,
	str20 = 20000,
	pgt2 = 20000,
	pgt34 = 20000,
	amggtbs = 20000,
	amggtrr20 = 20000,
	e30c = 20000,
	a90pit = 20000,
	ar6c = 20000,
	rmodpagani = 20000,
	rmodm4 = 20000,
	aventador = 20000,
	rmodbmwi8 = 20000,
	['2017chiron'] = 20000,
	['2019chiron'] = 20000,
	['918'] = 20000,
	aventadors = 20000,
	ie = 20000,
	ctr3 = 20000,
	['570S'] = 20000,
	['650s'] = 20000,
	mp412c = 20000,
	m8gte = 20000,
	f458 = 20000,
	f430s = 20000,
	majimalm = 20000,
	gt17 = 20000,
	terzo = 20000,
	p1lm = 20000,
	c8r = 20000,
	-- Ferrari Collection
	['488'] = 20000,
	f812 = 20000,
	fct = 20000,
	fxxk = 20000,
	laferrari = 20000,
	mig = 20000,
	yFe458 = 20000,
	yFeF12 = 20000,
	-- Lamborghini Collection
	huracanst = 20000,
	lambose = 20000,
	lp670sv = 20000,
	lp700r = 20000,
	svj63 = 20000,
	urus = 20000,
	veneno = 20000,
	-- McLaren Collection
	['675lt'] = 20000,
	['720s'] = 20000,
	gtr96 = 20000,
	mcst = 20000,
	senna = 20000,
	-- Bugatti Collection
	bolide = 20000,

	-- ========================================
	-- السيارات الفاخرة (65 سيارة) - حمولة 30000
	-- ========================================
	cognoscenti = 30000,
	cognoscenti2 = 30000,
	limo2 = 30000,
	schafter2 = 30000,
	schafter3 = 30000,
	schafter4 = 30000,
	schafter5 = 30000,
	schafter6 = 30000,
	superd = 30000,
	windsor = 30000,
	windsor2 = 30000,
	xls = 30000,
	xls2 = 30000,
	['2013rs7'] = 30000,
	benzs600 = 30000,
	caprice13 = 30000,
	caprice89 = 30000,
	camry55 = 30000,
	nisaltima = 30000,
	['750li'] = 30000,
	benzc32 = 30000,
	bmwe38 = 30000,
	bmwe65 = 30000,
	camry18 = 30000,
	rrphantom = 30000,
	rculi = 30000,
	['96impala'] = 30000,
	['750li2'] = 30000,
	taurus = 30000,
	gs350 = 30000,
	taxi2 = 30000,
	pullman = 30000,
	rs6 = 30000,
	c10custom = 30000,
	panamera17turbo = 30000,
	ct5v = 30000,
	['2019M5'] = 30000,
	sonata20 = 30000,
	tmodel = 30000,
	nomad56 = 30000,
	belair56 = 30000,
	beetle74 = 30000,
	['2018s650p'] = 30000,
	e63b = 30000,
	wraith = 30000,
	rrghost21 = 30000,
	gsf = 30000,
	rs62 = 30000,
	rocket = 30000,
	jettagli = 30000,
	rs7c8 = 30000,
	ren_clio_5 = 30000,
	capi20 = 30000,
	taycan = 30000,
	teslapd = 30000,
	ocnetrongt = 30000,
	regalia = 30000,
	onebeast = 30000,
	gcart = 30000,
	ct660tow = 30000,
	['337flatbed'] = 30000,
	uparmorhmvdes = 30000,
	hmmwv = 30000,
	tfft = 30000,
	ben17 = 30000,
	s63amg18 = 30000,
	['911gt3'] = 30000,
	gtam21 = 30000,
	tesroad20 = 30000,
	m422 = 30000,
	alfmito = 30000,
	sl65bs = 30000,
	bloa5 = 30000,

	-- ========================================
	-- الدراجات النارية (78 دراجة) - حمولة 8000
	-- ========================================
	akuma = 8000,
	bagger = 8000,
	bati = 8000,
	bati2 = 8000,
	bf400 = 8000,
	carbonrs = 8000,
	cliffhanger = 8000,
	daemon = 8000,
	daemon2 = 8000,
	defiler = 8000,
	double = 8000,
	enduro = 8000,
	faggio = 8000,
	faggio2 = 8000,
	faggio3 = 8000,
	gargoyle = 8000,
	hakuchou = 8000,
	hakuchou2 = 8000,
	hexer = 8000,
	innovation = 8000,
	lectro = 8000,
	nemesis = 8000,
	pcj = 8000,
	ruffian = 8000,
	sanchez = 8000,
	sanchez2 = 8000,
	sovereign = 8000,
	thrust = 8000,
	vader = 8000,
	vindicator = 8000,
	vortex = 8000,
	wolfsbane = 8000,
	zombiea = 8000,
	zombieb = 8000,
	diablous = 8000,
	diablous2 = 8000,
	fcr = 8000,
	fcr2 = 8000,
	manchez = 8000,
	nightblade = 8000,
	ratbike = 8000,
	shotaro = 8000,
	chimera = 8000,
	esskey = 8000,
	avarus = 8000,
	sanctus = 8000,
	raptor = 8000,
	blazer4 = 8000,
	-- Specialized Bikes
	yzfr6 = 8000,
	goldwing = 8000,
	gsx1000 = 8000,
	softail1 = 8000,
	bcr1drag = 8000,
	bmws = 8000,
	zx10 = 8000,
	z1000 = 8000,
	hayabusa = 8000,
	rc = 8000,
	d99 = 8000,
	f4rr = 8000,
	cb650r = 8000,
	['06sx2t'] = 8000,
	dv4r = 8000,
	mv2019 = 8000,
	['160montada'] = 8000,
	dream125 = 8000,
	XADV = 8000,
	c150 = 8000,
	rc213vs = 8000,
	['21rc16'] = 8000,
	dsmc21 = 8000,
	dsmc19 = 8000,
	gsxrr21 = 8000,
	lrc213v = 8000,
	m119 = 8000,
	m121 = 8000,
	rc213vr = 8000,
	rsgp21 = 8000,
	-- Custom Bikes
	tf_bike = 8000,
	boostedzach = 8000,
	tempestaevo = 8000,
	['4thgenss'] = 8000,
	ruby = 8000,
	['94streetgt'] = 8000,
	['99sboosted'] = 8000,
	tyifens68 = 8000,
	streetsupra = 8000,
	streetscience = 8000,
	sabregt1 = 8000,
	rtr = 8000,
	s2k = 8000,
	s10 = 8000,
	dragnova = 8000,
	maliboohoo = 8000,
	dragm2 = 8000,
	gsnake = 8000,
	gfreemod = 8000,
	gclapped = 8000,
	egd = 8000,
	dukes1 = 8000,
	dragfd = 8000,
	dragekcivick = 8000,
	dragek = 8000,
	dodgesrt2 = 8000,
	sfwdcrx = 8000,
	['2020CopoV2'] = 8000,
	['2019CobraJet'] = 8000,
	camaross = 8000,

	-- السيارات الجديدة المضافة - فئة دراجات نارية
	ducatipanigalev4 = 8000,
	ninjah2carbon = 8000,
	bmws1000rr2020 = 8000,
	yamahar1m = 8000,
	cbr1000rr = 8000,
	gsxr1000 = 8000,
	apriliarsv4 = 8000,
	ktm1290 = 8000,
	speedtriple = 8000,
	harleyfatboy = 8000,
	indianscout = 8000,
	mvagustaf3 = 8000,
	benellitnt = 8000,
	husqvarna701 = 8000,
	royalenfield = 8000,

	-- ========================================
	-- سيارات الشرطة (15 سيارة) - حمولة 50000
	-- ========================================
	['ah-police1'] = 50000,
	['ah-police2'] = 50000,
	['ah-police3'] = 50000,
	['ah-police5'] = 50000,
	['ah-police6'] = 50000,
	['ah-police10'] = 50000,
	['ah-police11'] = 50000,
	['ah-police12'] = 50000,
	['ah-police13'] = 50000,
	['ah-police14'] = 50000,
	['ah-police15'] = 50000,
	['ah-police16'] = 50000,
	['ah-police17'] = 50000,
	['ah-police18'] = 50000,
	['ah-police19'] = 50000,

	-- ========================================
	-- الشاحنات والمقطورات (137 شاحنة) - حمولة 80000
	-- ========================================
	benson = 80000,
	biff = 80000,
	cerberus = 80000,
	cerberus2 = 80000,
	cerberus3 = 80000,
	hauler = 80000,
	hauler2 = 80000,
	mule = 80000,
	mule2 = 80000,
	mule3 = 80000,
	mule4 = 80000,
	packer = 80000,
	phantom = 80000,
	phantom2 = 80000,
	phantom3 = 80000,
	pounder = 80000,
	pounder2 = 80000,
	stockade = 80000,
	stockade3 = 80000,
	-- Heavy Trucks
	d7r = 80000,
	cat259 = 80000,
	oiltanker = 80000,
	cat745c = 80000,
	dump2 = 80000,
	tiptruck3 = 80000,
	tiptruck4 = 80000,
	rubble2 = 80000,
	sandkinghd = 80000,
	packer2 = 80000,
	phantom4 = 80000,
	ct660dump = 80000,
	ct660 = 80000,
	['17jamb'] = 80000,
	hauler4 = 80000,
	muletip = 80000,
	hauler5 = 80000,
	hauler6 = 80000,
	steed = 80000,
	yankee = 80000,
	yankee2 = 80000,
	forklift2 = 80000,
	hauler7 = 80000,
	benson3 = 80000,
	hauler8 = 80000,
	fhauler = 80000,
	mbhome = 80000,
	chantom = 80000,
	chantom2 = 80000,
	-- American Trucks
	ram1500 = 80000,
	gmcat4 = 80000,
	silverado2 = 80000,
	denali18 = 80000,
	ramlh20 = 80000,
	czr2 = 80000,
	amarok = 80000,
	['18ram'] = 80000,
	bad250 = 80000,
	bc20hd = 80000,
	bc2500hd = 80000,
	toyhauler = 80000,
	bcss = 80000,
	transformer = 80000,
	richobs = 80000,
	foxct = 80000,
	silve = 80000,
	pete351 = 80000,
	unimog = 80000,
	type266 = 80000,
	sierra88 = 80000,
	white55 = 80000,
	c10 = 80000,
	silv86 = 80000,
	ken49 = 80000,
	kw900 = 80000,
	W900 = 80000,
	bchauler = 80000,
	tooltruck = 80000,
	-- Cargo Trucks
	bensonc = 80000,
	bensonc2 = 80000,
	phantomhd = 80000,
	phantomcola = 80000,
	starbucksmule1 = 80000,
	starbucksmule2 = 80000,
	rtruck = 80000,
	benson2 = 80000,
	tiptruck5 = 80000,
	thauler = 80000,
	semihauler = 80000,
	chauler = 80000,
	hauler6_trailer = 80000,
	ehauler = 80000,
	thauler2 = 80000,
	seadoohauler = 80000,
	shauler = 80000,
	botdumptr = 80000,
	dumptr = 80000,
	['3500flatbed'] = 80000,
	T440box = 80000,
	boxlongtr = 80000,
	newsvan = 80000,
	newsvan2 = 80000,
	['20silv3500work'] = 80000,
	fordtanker = 80000,
	['18f350trailer'] = 80000,
	semi = 80000,
	docktrailer2 = 80000,
	trailerlogs2 = 80000,
	trailerswb = 80000,
	trailerswb2 = 80000,
	lowboy = 80000,
	lowboystinger = 80000,
	lowboyjeep = 80000,
	pjtrailer = 80000,
	trailer01 = 80000,
	trailer02 = 80000,
	trailer03 = 80000,
	trailer04 = 80000,
	trailer05 = 80000,
	hometrailer = 80000,
	nbtrailer = 80000,
	yftrailer = 80000,
	ptrailer = 80000,
	bcfueltanker = 80000,
	trailerscola = 80000,
	-- Mechanic Trucks
	TSmech1 = 80000,
	TSmech2 = 80000,
	TSmech3 = 80000,
	TSmech4 = 80000,
	TSmech5 = 80000,
	TSmech6 = 80000,
	TSmech7 = 80000,
	TSmech8 = 80000,
	TSmech9 = 80000,
	TSmech10 = 80000,
	TSmech11 = 80000,
	TSmech12 = 80000,
	TSmech13 = 80000,
	TSmech14 = 80000,
	gtow = 80000,
	S52 = 80000,
	-- Armored Trucks
	brickadeb1 = 80000,
	brickadeb2 = 80000,
	brickadeb3 = 80000,
	brickadeb4 = 80000,
	brickadeb5 = 80000,
	brickadeb6 = 80000,
	brickadeb7 = 80000,
	brickadeb8 = 80000,

	-- السيارات الجديدة المضافة - فئة شاحنات
	fordf450 = 80000,
	silverado3500 = 80000,
	ram5500 = 80000,
	volvofh16 = 80000,
	scaniar730 = 80000,
	mantgx = 80000,
	ivecostralis = 80000,
	renaultthigh = 80000,
	dafxf = 80000,
	actrosmp4 = 80000,
	peterbilt389 = 80000,
	mackanthem = 80000,
	freightlinercascadia = 80000,
	internationallonestar = 80000,
	tacomatro = 80000,

	-- السيارات الجديدة المضافة - فئة حمولة 120 كيلو - حمولة 120000
	f150raptor = 120000,          -- فورد F-150 رابتور
	ram1500 = 120000,             -- دودج رام 1500
	silverado1500 = 120000,       -- شيفروليه سيلفرادو 1500
	tundra2022 = 120000,          -- تويوتا تندرا 2022
	titan2021 = 120000,           -- نيسان تايتن 2021
	ridgeline = 120000,           -- هوندا ريدجلاين
	ranger2022 = 120000,          -- فورد رينجر 2022
	colorado2021 = 120000,        -- شيفروليه كولورادو 2021
	frontier2022 = 120000,        -- نيسان فرونتير 2022
	gladiator = 120000,           -- جيب جلادياتور

	-- السيارات الجديدة المضافة - فئة حمولة 80 كيلو - حمولة 80000
	transit2022 = 80000,          -- فورد ترانزيت 2022
	sprinter2021 = 80000,         -- مرسيدس سبرينتر 2021
	promaster = 80000,            -- رام بروماستر
	express2500 = 80000,          -- شيفروليه اكسبرس 2500
	savana2500 = 80000,           -- جمس سافانا 2500
	nv200 = 80000,                -- نيسان NV200
	metris = 80000,               -- مرسيدس متريس
	crafter = 80000,              -- فولكس واجن كرافتر
	ducato = 80000,               -- فيات دوكاتو
	boxer = 80000,                -- بيجو بوكسر

	-- السيارات الجديدة المضافة - فئة حمولة 60 كيلو - حمولة 60000
	hilux2022 = 60000,            -- تويوتا هايلكس 2022
	navara2021 = 60000,           -- نيسان نافارا 2021
	dmax2022 = 60000,             -- ايسوزو D-Max 2022
	bt50 = 60000,                 -- مازدا BT-50
	l200 = 60000,                 -- ميتسوبيشي L200
	amarok2021 = 60000,           -- فولكس واجن أماروك 2021
	xclass = 60000,               -- مرسيدس X-Class
	alaskan = 60000,              -- رينو ألاسكان
	fullback = 60000,             -- فيات فولباك
	strada = 60000,               -- فيات ستردا

	-- السيارات الجديدة المضافة - فئة حمولة 40 كيلو - حمولة 40000
	hiluxsingle = 40000,          -- تويوتا هايلكس كابينة مفردة
	navarasingle = 40000,         -- نيسان نافارا كابينة مفردة
	dmaxsingle = 40000,           -- ايسوزو D-Max كابينة مفردة
	bt50single = 40000,           -- مازدا BT-50 كابينة مفردة
	l200single = 40000,           -- ميتسوبيشي L200 كابينة مفردة
	ranger2door = 40000,          -- فورد رينجر كابينة مفردة
	colorado2door = 40000,        -- شيفروليه كولورادو كابينة مفردة
	frontier2door = 40000,        -- نيسان فرونتير كابينة مفردة
	tacoma2door = 40000,          -- تويوتا تاكوما كابينة مفردة
	canyon2door = 40000,          -- جمس كانيون كابينة مفردة

	-- السيارات الجديدة المضافة - فئة SUV متوسطة الحجم - حمولة 60000
	pilot2022 = 60000,            -- هوندا بايلوت 2022
	highlander2022 = 60000,       -- تويوتا هايلاندر 2022
	pathfinder2022 = 60000,       -- نيسان باثفايندر 2022
	cx9 = 60000,                  -- مازدا CX-9
	acadia = 60000,               -- جمس أكاديا
	traverse = 60000,             -- شيفروليه ترافرس
	atlas = 60000,                -- فولكس واجن أطلس
	telluride = 60000,            -- كيا تيلورايد
	palisade = 60000,             -- هيونداي باليسيد
	durango2022 = 60000,          -- دودج درانجو 2022

	-- السيارات الجديدة المضافة - فئة SUV كبيرة الحجم - حمولة 80000
	suburban2022 = 80000,         -- شيفروليه سوبربان 2022
	yukonxl = 80000,              -- جمس يوكن XL
	expeditionmax = 80000,        -- فورد اكسبيديشن ماكس
	sequoia2022 = 80000,          -- تويوتا سيكويا 2022
	armada2022 = 80000,           -- نيسان أرمادا 2022
	qx80_2022 = 80000,            -- انفينيتي QX80 2022
	lx600 = 80000,                -- لكزس LX 600
	gls580 = 80000,               -- مرسيدس GLS 580
	x7m50i = 80000,               -- بي ام دبليو X7 M50i
	cayenneturbo = 80000,         -- بورش كايين توربو

	-- السيارات الجديدة المضافة - فئة كهربائية وهجينة - حمولة 35000
	model3 = 35000,               -- تيسلا موديل 3
	modely = 35000,               -- تيسلا موديل Y
	modelx = 35000,               -- تيسلا موديل X
	etrongt = 35000,              -- اودي e-tron GT
	taycan4s = 35000,             -- بورش تايكان 4S
	eqc = 35000,                  -- مرسيدس EQC
	ix3 = 35000,                  -- بي ام دبليو iX3
	ioniq5 = 35000,               -- هيونداي أيونيك 5
	ev6 = 35000,                  -- كيا EV6
	leaf2022 = 35000,             -- نيسان ليف 2022

	-- السيارات الجديدة المضافة - فئة رياضية كهربائية - حمولة 25000
	roadster2022 = 25000,         -- تيسلا رودستر 2022
	taycanturbo = 25000,          -- بورش تايكان توربو S
	etrongtrs = 25000,            -- اودي e-tron GT RS
	eqsامg = 25000,               -- مرسيدس EQS AMG
	i4m50 = 25000,                -- بي ام دبليو i4 M50
	mustangmache = 25000,         -- فورد موستانج ماك-E GT
	polestar1 = 25000,            -- بولستار 1
	lucidairs = 25000,            -- لوسيد اير سابين
	plaid = 25000,                -- تيسلا موديل S بلايد
	rimacnevera = 25000,          -- ريماك نيفيرا

	-- السيارات الجديدة المضافة - فئة كلاسيكية - حمولة 35000
	mustang67 = 35000,            -- فورد موستانج 1967
	camaro69ss = 35000,           -- شيفروليه كامارو SS 1969
	challenger70 = 35000,         -- دودج تشالنجر 1970
	corvette63 = 35000,           -- شيفروليه كورفيت 1963
	gto64 = 35000,                -- بونتياك GTO 1964
	chevelle70 = 35000,           -- شيفروليه شيفيل SS 1970
	roadrunner70 = 35000,         -- بليموث رود رانر 1970
	cuda70 = 35000,               -- بليموث كودا 1970
	charger69 = 35000,            -- دودج تشارجر 1969
	impala67 = 35000,             -- شيفروليه إمبالا 1967

	-- السيارات الجديدة المضافة - فئة فخمة إضافية - حمولة 20000
	phantom8 = 20000,             -- رولز رويس فانتوم الجيل الثامن
	cullinanblack = 20000,        -- رولز رويس كولينان بلاك بادج
	bentayga = 20000,             -- بنتلي بنتايجا
	continental = 20000,          -- بنتلي كونتيننتال GT
	flying_spur = 20000,          -- بنتلي فلاينج سبير
	urus_lambo = 20000,           -- لامبورغيني أوروس
	dbx = 20000,                  -- أستون مارتن DBX
	vantage = 20000,              -- أستون مارتن فانتاج
	levante = 20000,              -- مازيراتي ليفانتي
	ghibli = 20000,               -- مازيراتي غيبلي

	-- ========================================
	-- الطائرات (46 طائرة) - حمولة 150000
	-- ========================================
	alpha = 150000,
	besra = 150000,
	cargoplane = 150000,
	cuban800 = 150000,
	dodo = 150000,
	duster = 150000,
	howard = 150000,
	jet = 150000,
	lazer = 150000,
	luxor = 150000,
	luxor2 = 150000,
	mammatus = 150000,
	miljet = 150000,
	nimbus = 150000,
	shamal = 150000,
	stunt = 150000,
	titan = 150000,
	velum = 150000,
	velum2 = 150000,
	vestra = 150000,
	volatol = 150000,
	-- Commercial Aircraft
	saab2000 = 150000,
	tu154m = 150000,
	['707'] = 150000,
	['747'] = 150000,
	['757'] = 150000,
	['773er'] = 150000,
	['788'] = 150000,
	['737200'] = 150000,
	a319 = 150000,
	a321neo = 150000,
	a333 = 150000,
	a343 = 150000,
	a350 = 150000,
	a380 = 150000,
	an2 = 150000,
	atr72 = 150000,
	avashut = 150000,
	b727 = 150000,
	b727c = 150000,
	bac = 150000,
	dc10 = 150000,
	emb100 = 150000,
	emb120 = 150000,
	emb175 = 150000,
	emb390fedex = 150000,
	emb145 = 150000,
	emb190 = 150000,
	emb1000 = 150000,
	fokker100 = 150000,
	il62m = 150000,
	il76 = 150000,
	il96 = 150000,
	l1011 = 150000,
	lcf = 150000,
	e190e2 = 150000,
	['208'] = 150000,
	['747sp'] = 150000,
	airglider = 150000,

	-- ========================================
	-- المروحيات (9 مروحيات) - حمولة 100000
	-- ========================================
	annihilator = 100000,
	buzzard = 100000,
	buzzard2 = 100000,
	cargobob = 100000,
	cargobob2 = 100000,
	cargobob3 = 100000,
	cargobob4 = 100000,
	frogger = 100000,
	frogger2 = 100000,

	-- ========================================
	-- القوارب واليخوت (24 قارب) - حمولة 60000
	-- ========================================
	dinghy = 60000,
	dinghy2 = 60000,
	dinghy3 = 60000,
	dinghy4 = 60000,
	jetmax = 60000,
	marquis = 60000,
	seashark = 60000,
	seashark2 = 60000,
	seashark3 = 60000,
	speeder = 60000,
	speeder2 = 60000,
	squalo = 60000,
	submersible = 60000,
	submersible2 = 60000,
	suntrap = 60000,
	toro = 60000,
	toro2 = 60000,
	tropic = 60000,
	tropic2 = 60000,
	tug = 60000,
	predator = 60000,
	-- New Boats
	nitroboat = 60000,
	sr510 = 60000,
	sr650fly = 60000,
	yacht2 = 60000,
	franco125 = 60000,
	contender39 = 60000,
	seadoogti215 = 60000,
	tritoon = 60000,
	['42ftyellowfin'] = 60000,
	yaluxe = 60000,
	amels200 = 60000,
	gfboat = 60000,
	fxho = 60000,
	explorer = 60000,
	hcbr17 = 60000,

	hilux1 = 40000,
	bison = 40000,
	kangoo = 40000,
	youga3 = 40000,
	UTILTRUC = 100000,
	hilux1 = 40000,
	REBEL02 = 40000,
	rumpo = 40000,
	burrito3 = 40000,
	burrito2 = 40000,
	burrito = 40000,
	gruppe1 = 40000,
	gruppe3 = 40000,
	gruppe17 = 40000,
	gruppe2 = 40000,
	gruppe16 = 40000,
	gruppe15 = 40000,
	gruppe18 = 40000,
	gruppe6 = 40000,
	polkuw11 = 40000,
	polkuw21 = 40000,
	polkuw13 = 40000,
	polkuw22 = 40000,
	polkuw00 = 40000,
	polkuw66 = 40000,
	polkuw77 = 40000,
	polkuw88 = 40000,
	polkuw35 = 40000,
	polkuw33 = 40000,
	polkuw06 = 40000,
	polkuw77 = 40000,
	polkuw30 = 40000,
	polkuw31 = 40000,
	polkuw32 = 40000,
	polkuw34 = 40000,
	polkuw02 = 40000,
	polkuw16 = 40000,
	polkuw56 = 40000,
	mixer2 = 40000,
	rebel2 = 60000, -- زيادة الحمولة إلى 60 كيلو


	avalon = 15000,
	benzs600 = 15000,
	Camry11 = 15000,
	caprice17 = 15000,
	crownvic2011 = 15000,
	ct5v = 15000,
	gcmaccent15 = 15000,
	gcmelantra19 = 15000,
	gcmpassat12 = 15000,
	lex350 = 15000,
	lex500 = 15000,
	optima = 15000,
	panamera17turbo = 15000,
	s600w220 = 15000,
	sonata20 = 15000,
	soso18 = 15000,
	towncar2010 = 15000,




	bmwx6 = 25000,
	cherokee1 = 25000,
	expmax20 = 25000,
	g5004x4 = 25000,
	g632019 = 25000,
	g65amg = 25000,
	gclass = 25000,
	gmcyd = 25000,
	granger = 25000,
	hdd = 25000,
	jeep2012 = 25000,
	lex570 = 25000,
	lex57015 = 25000,
	lxs = 25000,
	lxs_2019 = 25000,
	q820 = 25000,
	qx80 = 25000,
	rb3_2006 = 25000,
	rb3_2017 = 25000,
	rrst = 25000,
	subn = 25000,
	taho89 = 25000,
	tahoe = 25000,
	tahoe21 = 25000,
	type262 = 120000,
	vxr_2020 = 25000,
	w463a = 25000,





	['13fmb302'] = 10000,
	['16challenger'] = 10000,
	['16charger2'] = 10000,
	['16ss'] = 10000,
	['19dbs'] = 10000,
	['2013rs7'] = 10000,
	a6  = 10000,
	amggt63s = 10000,
	c7 = 10000,
	c8 = 10000,
	cls63s = 10000,
	ctsv16 = 10000,
	demon = 10000,
	dtd_c63s = 10000,
	furoregt = 10000,
	gcm992gt3 = 10000,
	gcm992targa = 10000,
	gtr = 10000,
	m3e30 = 10000,
	m3e46 = 10000,
	m3f80 = 10000,
	mach1 = 10000,
	mbc63 = 10000,
	mustang19 = 10000,
	r820 = 10000,
	rmodbmwi8 = 10000,
	S63W222 = 10000,
	supra2 = 10000,
	zn20 = 10000,
	
	
	
	['650s'] = 5000,
	aventadors = 5000,
	bcps = 5000,
	c8r = 5000,
	f430s = 5000,
	fxxkevo = 5000,
	ie = 5000,
	mcgt20 = 5000,
	rmodpagani = 5000,
	
	

	['745le'] = 18000,
	ben17 = 18000,
	bfs14 = 18000,
	binkshf = 18000,
	bmm = 18000,
	cullinan = 18000,
	dawnonyx = 18000,
	genesisg90 = 18000,
	ghostewb1 = 18000,
	s500w222 = 18000,
	wraithb = 18000,



	['560sec87'] = 18000,
	['750il'] = 18000,
	Benz300SEL = 18000,
	boss302 = 18000,
	camaro68 = 18000,
	camaro69 = 18000,
	caprice91 = 18000,
	caprice93 = 18000,
	caprs = 18000,
	casco = 18000,
	chall70 = 18000,
	corvette63 = 18000,
	f40 = 18000,
	firebird = 18000,
	firebird77 = 18000,
	gsxb = 18000,
	impala672 = 18000,
	impala96 = 18000,
	ldsv = 18000,
	mb300sl = 18000,
	mercw126 = 18000,
	mustang68 = 18000,
	silver67 = 18000,
	towncar91 = 18000,
	trans69 = 18000,
	vigero = 18000,
	z2879 = 18000,
	
	
	
	
	['20r1'] = 5000,
	aeroxdrag = 5000,
	aeroxr = 5000,
	banshee87 = 5000,
	flhxs_streetglide_special18 = 5000,
	goldwing = 5000,
	hakuchou2 = 5000,
	hayabusa = 5000,
	nightblade = 5000,
	ninjah2 = 5000,
	rmz85cc = 5000,
	sanchez2 = 5000,
	tmax = 5000,
	
	
	
	
	['10ram'] = 120000,
	['18f350d'] = 120000,
	['19tundra'] = 120000,
	['20denalihd'] = 120000,
	ford20120 = 120000,
	g63amg6x6 = 120000,
	gmcat42 = 120000,
	gmcr20120 = 120000,
	silvr20120 = 120000,	
	
	
	
	
	
	['20f250'] = 80000,
	gmc9080 = 80000,
	silv2080 = 80000,
	silv6580 = 80000,
	silv86 = 80000,	


	ertgg = 60000,
	erttt = 60000,
	silv1560 = 60000,
	silv5860 = 60000,


	['34tt'] = 40000,
	tttee = 40000,

	polkuw18 = 40000,
	unmarked11 = 40000,
	unmarked15 = 40000,
	unmarked17 = 40000,
	unmarked13 = 40000,
	unmarked14 = 40000,
	unmarked10 = 40000,
	unmarked8 = 40000,
	pol3 = 40000,
	pol5 = 40000,
	polchall70 = 40000,
	riot = 40000,
	riot4 = 40000,
	sspres = 40000,
	panto99 = 40000,
	FBI = 40000,
	FIBjackal = 40000,
	C10 = 40000,
	["65C10"] = 40000,
	f100 = 40000,
	hilux1 = 40000,
	ems3 = 40000,
	ems4 = 40000,
	ems5 = 40000,
	ems6 = 40000,
	ems7 = 40000,
	ems8 = 40000,
	ems9 = 100000,


	-- حمولة 60 كجم --
	ddsn15 = 60000,
	sierra88 = 60000,
	silv1560 = 60000,
	silv5860 = 60000,
	gmc1500 = 60000,
	landv6 = 60000,
	silv20 = 60000,
	f15078 = 60000,
	f1501 = 60000,
	czr2 = 60000,
	ddsn19 = 60000,

	-- حمولة 80 كجم --
	f25020 = 80000,
	gmc9080 = 80000,
	silv2080 = 80000,
	silv6580 = 80000,
	ram2500 = 80000,
	["86k30"] = 80000,
	sierra06 = 80000,
	denali18 = 80000,
	sdgmc = 80000,
	nissantitan17 = 80000,
	f25020 = 80000,
	gmc9080 = 80000,
	gmcat42 = 80000,


	-- حمولة 100 كجم --
	ford20120 = 120000,
	g63amg6x6 = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,


	-- حمولة 120 كجم --
	["18f350ds"] = 120000,
	["20denalihd"] = 120000,
	bad250 = 120000,
	bc20kodiak = 120000,
	bcal450 = 120000,
	["3500sd"] = 120000,
	silv2500hd = 120000,
	ram10 = 120000,
	sprinter211 = 120000,
	e15082 = 120000,
	youga = 120000,
	transformer = 120000,
	speedo = 120000,
	f350d18 = 120000,
	ford20120 = 120000,
	g63amg6x6 = 120000,
	silvr20120 = 120000,

	-- حمولة 250 كجم --
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	["dnh"] = 120000,

	-- حمولة 350 كجم --
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,


	-- حمولة 520 كجم --
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,

	-- حمولات مركبات سبورت--
	fmb30213 = 20000,
	amggt63s = 20000,
	challenger16 = 20000,
	-- حمولات مركبات سيدان--
	a6 = 20000,
	avalon = 20000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات سوبر--
	c7 = 20000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات صالون--
	benzs600 = 40000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات فخمة--
	ben17 = 20000,
	bfs14 = 20000,
	binkshf = 20000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات كبير--
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات كلاسيكي--
	boss302 = 30000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات دراجات نارية--

	seashark = 20000,
	gmcyd = 30000,
	gmt900escalade = 30000,
	urus = 30000,
	-- حمولة
	contender = 40000,
	amarok = 60000,
	denalihd = 120000,
	hd193500 = 120000,
	sandkingswb = 60000,
	d863500 = 120000,
	silv203500 = 120000,
	rumpo3 = 120000,
	e15082 = 160000,
	d00f350d = 160000,
	raptor2017 = 160000,
	silverado99 = 160000,
	silv20 = 80000,
	f150 = 80000,
	trailboss20 = 80000,
	f25020 = 80000,
	--معرض الشاحنات
	unimog = 250000,
	vnl780 = 40000,
	dnh = 250000, 
	phantom = 40000,
	ct660 = 40000,
	scaniar730 = 40000,
	trans_mbenzarocs = 40000,
	ct660dump = 350000,
	biff = 350000,
		
	tiptruck = 350000,
	tiptruck2 = 350000,
	dumptr = 350000,
	mule2 = 250000,
	botdumptr = 250000,
	benson = 250000,
	pounder = 250000,
	tourbus = 40000,
	rubble = 250000,
	--معرض الشاحنات END
	seashark = 20000,
	suntrap = 30000,
	jetmax = 50000,
	speeder = 70000,
	sr650fly = 200000,
	toro = 70000,
	MARQUIS = 150000,
	predator = 30000,
	hauler = 30000,
	packer = 30000,
	phantom = 30000,
	asea = 20000,
	tug = 20000,
	w900 = 60000,

	avalon = 15000,
	benzs600 = 15000,
	Camry11 = 15000,
	caprice17 = 15000,
	crownvic2011 = 15000,
	ct5v = 15000,
	gcmaccent15 = 15000,
	gcmelantra19 = 15000,
	gcmpassat12 = 15000,
	lex350 = 15000,
	lex500 = 15000,
	optima = 15000,
	panamera17turbo = 15000,
	s600w220 = 15000,
	sonata20 = 15000,
	soso18 = 15000,
	towncar2010 = 15000,




	bmwx6 = 25000,
	cherokee1 = 25000,
	expmax20 = 25000,
	g5004x4 = 25000,
	g632019 = 25000,
	g65amg = 25000,
	gclass = 25000,
	gmcyd = 25000,
	granger = 25000,
	hdd = 25000,
	jeep2012 = 25000,
	lex570 = 25000,
	lex57015 = 25000,
	lxs = 25000,
	lxs_2019 = 25000,
	q820 = 25000,
	qx80 = 25000,
	rb3_2006 = 25000,
	rb3_2017 = 25000,
	rrst = 25000,
	subn = 25000,
	taho89 = 25000,
	tahoe = 25000,
	tahoe21 = 25000,
	vxr_2020 = 25000,
	w463a = 25000,





	['13fmb302'] = 10000,
	['16challenger'] = 10000,
	['16charger2'] = 10000,
	['16ss'] = 10000,
	['19dbs'] = 10000,
	['2013rs7'] = 10000,
	a6  = 10000,
	amggt63s = 10000,
	c7 = 10000,
	c8 = 10000,
	cls63s = 10000,
	ctsv16 = 10000,
	demon = 10000,
	dtd_c63s = 10000,
	furoregt = 10000,
	gcm992gt3 = 10000,
	gcm992targa = 10000,
	gtr = 10000,
	m3e30 = 10000,
	m3e46 = 10000,
	m3f80 = 10000,
	mach1 = 10000,
	mbc63 = 10000,
	mustang19 = 10000,
	r820 = 10000,
	rmodbmwi8 = 10000,
	S63W222 = 10000,
	supra2 = 10000,
	zn20 = 10000,
	
	
	
	['650s'] = 5000,
	aventadors = 5000,
	bcps = 5000,
	c8r = 5000,
	f430s = 5000,
	fxxkevo = 5000,
	ie = 5000,
	mcgt20 = 5000,
	rmodpagani = 5000,
	
	

	['745le'] = 18000,
	ben17 = 18000,
	bfs14 = 18000,
	binkshf = 18000,
	bmm = 18000,
	cullinan = 18000,
	dawnonyx = 18000,
	genesisg90 = 18000,
	ghostewb1 = 18000,
	s500w222 = 18000,
	wraithb = 18000,



	['560sec87'] = 18000,
	['750il'] = 18000,
	Benz300SEL = 18000,
	boss302 = 18000,
	camaro68 = 18000,
	camaro69 = 18000,
	caprice91 = 18000,
	caprice93 = 18000,
	caprs = 18000,
	casco = 18000,
	chall70 = 18000,
	corvette63 = 18000,
	f40 = 18000,
	firebird = 18000,
	firebird77 = 18000,
	gsxb = 18000,
	impala672 = 18000,
	impala96 = 18000,
	ldsv = 18000,
	mb300sl = 18000,
	mercw126 = 18000,
	mustang68 = 18000,
	silver67 = 18000,
	towncar91 = 18000,
	trans69 = 18000,
	vigero = 18000,
	z2879 = 18000,
	
	
	
	
	['20r1'] = 5000,
	aeroxdrag = 5000,
	aeroxr = 5000,
	banshee87 = 5000,
	flhxs_streetglide_special18 = 5000,
	goldwing = 5000,
	hakuchou2 = 5000,
	hayabusa = 5000,
	nightblade = 5000,
	ninjah2 = 5000,
	rmz85cc = 5000,
	sanchez2 = 5000,
	tmax = 5000,
	
	
	
	
	['18f350d'] = 120000,
	['19tundra'] = 120000,
	['20denalihd'] = 120000,
	ford20120 = 120000,
	g63amg6x6 = 120000,
	gmcat42 = 120000,
	gmcr20120 = 120000,
	silvr20120 = 120000,	
	
	
	
	
	
	['20f250'] = 80000,
	gmc9080 = 80000,
	landv62 = 80000,
	silv2080 = 80000,
	silv6580 = 80000,


	ertgg = 60000,
	erttt = 60000,
	silv1560 = 60000,
	silv5860 = 60000,


	['34tt'] = 40000,
	tttee = 40000,

	polkuw18 = 40000,
	unmarked11 = 40000,
	unmarked15 = 40000,
	unmarked17 = 40000,
	unmarked13 = 40000,
	unmarked14 = 40000,
	unmarked10 = 40000,
	unmarked8 = 40000,
	pol3 = 40000,
	pol5 = 40000,
	polchall70 = 40000,
	riot = 40000,
	riot4 = 40000,
	sspres = 40000,
	panto99 = 40000,
	FBI = 40000,
	FIBjackal = 40000,
	C10 = 40000,
	["65C10"] = 40000,
	f100 = 40000,
	hilux1 = 40000,
	ems3 = 40000,
	ems4 = 40000,
	ems5 = 40000,
	ems6 = 40000,
	ems7 = 40000,
	ems8 = 40000,
	ems9 = 100000,


	-- حمولة 60 كجم --
	ddsn15 = 60000,
	sierra88 = 60000,
	silv1560 = 60000,
	silv5860 = 60000,
	gmc1500 = 60000,
	landv6 = 60000,
	silv20 = 60000,
	f15078 = 60000,
	f1501 = 60000,
	czr2 = 60000,
	ddsn19 = 60000,





	transformer = 120000,
	bc20kodiak = 160000,
	bad250 = 160000,
	bc01sierrahd = 160000,
	bcal450 = 160000,
	silv2500hd = 120000,
	['193500hd'] = 120000,
	['65c10'] = 40000,
	['cherokee1'] = 40000,
	['bc21bronco'] = 40000,
	['czr2'] = 40000,
	['1500dj'] = 120000,
	['bc2500hd'] = 120000,
	['bcal450'] = 120000,
	['92dodgeram'] = 100000,
	['fordtanker'] = 100000,
	['20denalihd'] = 160000,
	['193500hd'] = 160000,
	['00f350dually'] = 160000,
	['bcal450'] = 160000,
	['18f350d'] = 160000,
	['bcf650'] = 160000,
	['bad250'] = 120000,
	['gmcat4'] = 80000,
	['trx'] = 60000,
	['2020silv'] = 80000,
	['17jamb'] = 250000,
	['benson3'] = 250000,
	['drybulktr'] = 250000,
	['ct660dump'] = 350000,
	['fueltr'] = 350000,
	['dump'] = 520000,
	['gmcs'] = 120000,
	['a6'] = 20000,
	['bcgeneral'] = 20000,
	['20f250'] = 80000,
	['denali18'] = 80000,
	['amarok'] = 60000,
	['bronco80'] = 60000,
	['20trailboss'] = 80000,
	['bcss'] = 80000,
	bcrzrxp = 20000,
	chevelle67 = 20000,
	clssb = 20000,
	e63amg = 20000,
	fc16 = 20000,
	focus3 = 20000,
	bcsportsman = 10000,
	seashark = 20000,
	mansm2 = 20000,
	mbe63 = 20000,
	mlnovitec = 20000,
	s63amg = 20000,
	smgt5 = 20000,
	terminator = 20000,
	w124 = 20000,
    suntrap = 30000,
    jetmax = 50000,
    k5blazer = 50000,
    speeder = 70000,
    sr650fly = 200000,
    toro = 70000,
    MARQUIS = 150000,
    predator = 30000,
    hauler = 30000,
    packer = 30000,
    phantom = 30000,
    ram2500 = 80000,
    wagoneer = 80000,
    f1501 = 80000,
    tug = 300000,
    f150 = 80000,
    gmcs = 60000,
    lifted86k10 = 120000,
    silv20 = 120000,
    wildtrak = 40000,
    hilux2019 = 60000,
    bcfabjeep = 60000,
	sadler6 = 80000,
	ct660 = 40000,
	bcls = 40000,
	foxanthem1 = 40000,
	kw900 = 40000,
	merckw = 40000,
	hilux12 = 40000,
	W900 = 40000,
	cat745c = 520000,
	ramlh20 = 80000,
	botdumptr  = 250000, -- 
	trailerswb = 250000, -- 
	biff = 250000, -- 
	benson2 = 250000, -- 
	benson = 250000, -- 
	['tractor2'] = 70000, -- 
	dumptr  = 350000, -- 
	fueltr = 350000, --
	trailerlogs2 = 350000, --
	trailerswb2 = 350000, --
	boxlongtr = 450000,

	-- ========================================
	-- السيارات الجديدة المضافة
	-- ========================================

	-- فئة سيدان - حمولة 35000
	['79mudrunner'] = 35000,
	drehellcatdurango = 35000,
	cavalcade2 = 35000,
	lc300gr = 35000,
	bodhi2 = 35000,
	relaxgmc = 35000,
	sierra2020 = 35000,
	rebel2 = 35000,
	gle22 = 35000,
	oracle2 = 35000,
	max99s = 35000,
	taurus23 = 35000,
	cm22 = 35000,

	-- فئة كبيرة - حمولة 50000
	landstalker = 50000,
	granger = 50000,
	rb32024 = 50000,
	gx21 = 50000,

	-- فئة سبورت - حمولة 30000
	drescatpack = 30000,
	bmwm5f90 = 30000,
	kuruma = 30000,
	ninef = 30000,
	c6z06 = 30000,
	rmode63s = 30000,
	['16charger'] = 30000,
	czr1 = 30000,

	-- فئة سوبر - حمولة 25000
	pgt322 = 25000,

	-- فئة كلاسيك - حمولة 35000
	btype2 = 35000,
	caprice89 = 35000,

	-- فئة حمولة 120 كيلو - حمولة 120000
	nissantitan17 = 120000,
	sadler = 120000,
	Burrito = 120000,
	moonbeam = 120000,

	-- فئة حمولة 40 كيلو - حمولة 40000
	amarok = 40000,

	-- السيارات الجديدة المضافة (الدفعة الثانية)
	-- فئة سيدان - حمولة 35000
	premier = 35000,
	caprice17 = 35000,
	oracle = 35000,

	-- فئة حمولة 120 كيلو - حمولة 120000
	comet2 = 120000,
	silvr20120 = 120000,

	-- السيارات الجديدة المضافة (الدفعة الثالثة)
	-- فئة سيدان - حمولة 35000
	benzs600 = 35000,
	Camry11 = 35000,
	vic11 = 35000,
	gm2005 = 35000,
	s600w220 = 35000,
	sonata20 = 35000,

	-- فئة كبيرة - حمولة 50000
	cherokee1 = 50000,
	expmax20 = 50000,
	g5004x4 = 50000,
	g632019 = 50000,
	g65amg = 50000,
	w223b50 = 50000,
	jeep2012 = 50000,
	lxs = 50000,
	lxs_2019 = 50000,
	rrst = 50000,
	taho89 = 50000,
	tahoe21 = 50000,
	vxr_2020 = 50000,
	w463a = 50000,

	-- فئة سبورت - حمولة 30000
	cls63s = 30000,
	ctsv16 = 30000,
	dtd_c63s = 30000,
	furoregt = 30000,
	gcm992gt3 = 30000,
	gcm992targa = 30000,
	m3e46 = 30000,
	m3f80 = 30000,
	mach1 = 30000,
	mbc63 = 30000,
	r820 = 30000,

	-- فئة سوبر - حمولة 25000
	f430s = 25000,
	fxxkevo = 25000,
	mcgt20 = 25000,
	rmodpagani = 25000,

	-- فئة فخمة - حمولة 20000
	ben17 = 20000,
	binkshf = 20000,
	bmm = 20000,
	cullinan = 20000,
	dawnonyx = 20000,
	genesisg90 = 20000,
	s500w222 = 20000,

	-- السيارات الجديدة المضافة - فئة فخمة
	maybachs680 = 20000,
	bentleymulsanne = 20000,
	rrspectre = 20000,
	astonrapide = 20000,
	jaguarxj = 20000,
	maseratiqs = 20000,
	lexusls500h = 20000,
	genesisg90new = 20000,
	escalade22 = 20000,
	navigator22 = 20000,
	infinitiqx80 = 20000,
	acuransx = 20000,
	teslasp = 20000,
	lucidair = 20000,
	rivianr1t = 20000,

	-- فئة كلاسيك - حمولة 35000
	boss302 = 35000,
	camaro68 = 35000,
	camaro69 = 35000,
	caprice91 = 35000,
	caprice93 = 35000,
	chall70 = 35000,
	corvette63 = 35000,
	firebird = 35000,
	firebird77 = 35000,
	gsxb = 35000,
	impala672 = 35000,
	impala96 = 35000,
	ldsv = 35000,
	mb300sl = 35000,
	mercw126 = 35000,
	mustang68 = 35000,
	silver67 = 35000,
	towncar91 = 35000,
	trans69 = 35000,
	vigero = 35000,
	z2879 = 35000,

	-- فئة حمولة 120 كيلو - حمولة 120000
	ford20120 = 120000,

	-- فئة حمولة 80 كيلو - حمولة 80000
	['20r1'] = 80000,
	aeroxdrag = 80000,
	aeroxr = 80000,
	banshee87 = 80000,
	flhxs_streetglide_special18 = 80000,
	goldwing = 80000,
	hayabusa = 80000,
	nightblade = 80000,
	ninjah2 = 80000,
	rmz85cc = 80000,
	sanchez2 = 80000,
	tmax = 80000,

	-- فئة حمولة 60 كيلو - حمولة 60000
	['18f350d'] = 60000,
	['19tundra'] = 60000,
	['20denalihd'] = 60000,
	gmcr20120 = 60000,

}

Config.VehiclePlate = {
	taxi        = "TAXI",
	cop         = "LSPD",
	ambulance   = "EMS0",
	mecano	    = "MECA",
}
